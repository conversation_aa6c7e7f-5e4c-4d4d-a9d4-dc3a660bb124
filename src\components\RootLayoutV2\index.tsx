import { AnimateRoute } from '@/router/AnimateRoute'
import clsx from 'clsx'
import { LAYOUT_SIZE } from '../RootLayout/constants'
import { VersionWrapper } from '../VersionWrapper'
import Navbar from './components/Navbar'
import NavbarPicBall from './components/NavbarPicBall'

export default function Home() {
  return <div
    className="h-screen flex flex-col overflow-hidden bg-[#EDF4FF] "
    style={{ padding: '0 1.5rem 1.5rem 1.5rem' }}
  // colors={ [
  //   ['#D1C4E9', 'rgba(235, 105, 78, 0)'],
  //   ['#CFD8DC', 'rgba(243, 11, 164, 0)'],
  //   ['#CDC8C2', 'rgba(254, 234, 131, 0)'],
  //   ['#FFF3E0', 'rgba(170, 142, 245, 0)'],
  //   ['#B0BEC5', 'rgba(248, 192, 147, 0)'],
  // ] }
  >

    <VersionWrapper
      zhComponent={
        <NavbarPicBall style={ {
          height: LAYOUT_SIZE.headerHeight,
        } } />
      }
      foreignComponent={
        <Navbar style={ {
          height: LAYOUT_SIZE.headerHeight,
        } } />
      }
    />

    <div
      className={ clsx(
        'flex',
      ) }
      style={ {
        height: `calc(100% - ${LAYOUT_SIZE.headerHeight}px)`,
      } }>
      
      {/* 左侧项目侧边栏 */}
      
      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col">
        <AnimateRoute />
      </div>
    </div>
  </div>
}
